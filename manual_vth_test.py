#!/usr/bin/env python3
"""
手动测试不同阈值电压的拟合效果
"""

import numpy as np
import matplotlib.pyplot as plt
from main import (
    actual_test_data, 
    run_spice, 
    extract_data, 
    update_spice_params
)

def test_threshold_voltage_range():
    """测试不同阈值电压的拟合效果"""
    
    # 基础参数
    base_params = {
        'param_MINT_Kp': 100.0,
        'param_MINT_U0': 600.0,
        'param_MINT_Nfs': 5e11,
        'param_MINT_Eta': 1000.0,
        'param_MINT_L': 2e-6,
        'param_MINT_W': 1e-4,
        'param_MINT_Gamma': 0.5,
        'param_MINT_Phi': 0.7,
        'param_MINT_Is': 1e-14,
        'param_MINT_Js': 1e-8,
        'param_MINT_Pb': 0.8,
        'param_MINT_Tox': 10e-9,
        'param_MINT_Xj': 0.2e-6,
        'param_MINT_Vmax': 1e5
    }
    
    # 测试不同的阈值电压
    vth_values = [2.0, 2.2, 2.4, 2.6, 2.8, 3.0]
    results = []
    
    actual_voltage = actual_test_data['v-sweep']
    actual_current = actual_test_data['vids#branch']
    
    print("测试不同阈值电压的效果...")
    
    for vth in vth_values:
        print(f"\n测试 Vth = {vth}V")
        
        # 设置参数
        test_params = base_params.copy()
        test_params['param_MINT_Vto'] = vth
        
        # 更新SPICE参数并运行仿真
        update_spice_params("./ngspice_circuit/lib/POWERMOS.lib", **test_params)
        run_spice()
        sim_data = extract_data("./ngspice_circuit/run_log.log")
        
        if sim_data:
            sim_keys = list(sim_data.keys())
            if len(sim_keys) >= 2:
                sim_voltage = np.array(sim_data[sim_keys[0]])
                sim_current = np.array(sim_data[sim_keys[1]])
                
                # 计算误差
                min_len = min(len(actual_current), len(sim_current))
                
                # 计算相对误差
                relative_errors = []
                log_errors = []
                
                for i in range(min_len):
                    actual_val = abs(actual_current[i])
                    sim_val = abs(sim_current[i])
                    
                    if actual_val > 1e-15:
                        rel_err = abs((sim_val - actual_val) / actual_val)
                        relative_errors.append(rel_err)
                        
                        # 对数误差
                        log_actual = np.log10(max(actual_val, 1e-15))
                        log_sim = np.log10(max(sim_val, 1e-15))
                        log_err = abs(log_sim - log_actual)
                        log_errors.append(log_err)
                
                if relative_errors and log_errors:
                    avg_rel_error = np.mean(relative_errors)
                    avg_log_error = np.mean(log_errors)
                    rms_rel_error = np.sqrt(np.mean(np.array(relative_errors)**2))
                    rms_log_error = np.sqrt(np.mean(np.array(log_errors)**2))
                    
                    results.append({
                        'vth': vth,
                        'avg_rel_error': avg_rel_error,
                        'avg_log_error': avg_log_error,
                        'rms_rel_error': rms_rel_error,
                        'rms_log_error': rms_log_error,
                        'sim_voltage': sim_voltage,
                        'sim_current': sim_current
                    })
                    
                    print(f"  平均相对误差: {avg_rel_error:.4f}")
                    print(f"  RMS相对误差: {rms_rel_error:.4f}")
                    print(f"  平均对数误差: {avg_log_error:.4f}")
                    print(f"  RMS对数误差: {rms_log_error:.4f}")
    
    return results

def plot_comparison(results):
    """绘制不同阈值电压的对比图"""
    
    if not results:
        print("没有结果可以绘制")
        return
    
    actual_voltage = actual_test_data['v-sweep']
    actual_current = actual_test_data['vids#branch']
    
    # 创建子图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 绘制实际数据
    ax1.semilogy(actual_voltage, actual_current, 'ro-', label='实际数据', markersize=3, linewidth=2)
    ax2.semilogy(actual_voltage, actual_current, 'ro-', label='实际数据', markersize=3, linewidth=2)
    
    # 绘制不同阈值电压的仿真结果
    colors = ['b-', 'g-', 'c-', 'm-', 'y-', 'k-']
    
    for i, result in enumerate(results):
        if i < len(colors):
            vth = result['vth']
            sim_voltage = result['sim_voltage']
            sim_current = result['sim_current']
            
            ax1.semilogy(sim_voltage, sim_current, colors[i], 
                        label=f'Vth={vth}V', linewidth=1.5, alpha=0.8)
    
    # 找到最佳结果
    best_result = min(results, key=lambda x: x['rms_log_error'])
    ax2.semilogy(best_result['sim_voltage'], best_result['sim_current'], 'g-', 
                label=f'最佳拟合 (Vth={best_result["vth"]}V)', linewidth=2)
    
    # 设置图表
    ax1.set_xlabel('栅极电压 Vgs (V)')
    ax1.set_ylabel('漏极电流 Id (A)')
    ax1.set_title('不同阈值电压的拟合对比')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    ax2.set_xlabel('栅极电压 Vgs (V)')
    ax2.set_ylabel('漏极电流 Id (A)')
    ax2.set_title('最佳拟合结果')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # 绘制误差分析
    vth_values = [r['vth'] for r in results]
    rms_rel_errors = [r['rms_rel_error'] for r in results]
    rms_log_errors = [r['rms_log_error'] for r in results]
    
    ax3.plot(vth_values, rms_rel_errors, 'bo-', label='RMS相对误差')
    ax3.set_xlabel('阈值电压 Vth (V)')
    ax3.set_ylabel('RMS相对误差')
    ax3.set_title('相对误差 vs 阈值电压')
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    
    ax4.plot(vth_values, rms_log_errors, 'ro-', label='RMS对数误差')
    ax4.set_xlabel('阈值电压 Vth (V)')
    ax4.set_ylabel('RMS对数误差')
    ax4.set_title('对数误差 vs 阈值电压')
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    
    plt.tight_layout()
    plt.savefig('threshold_voltage_analysis.png', dpi=300, bbox_inches='tight')
    print("\n分析图已保存为 'threshold_voltage_analysis.png'")
    
    # 打印最佳结果
    print(f"\n最佳阈值电压: {best_result['vth']}V")
    print(f"对应的RMS对数误差: {best_result['rms_log_error']:.4f}")
    print(f"对应的RMS相对误差: {best_result['rms_rel_error']:.4f}")

def main():
    """主函数"""
    print("开始手动测试阈值电压...")
    
    results = test_threshold_voltage_range()
    
    if results:
        plot_comparison(results)
    else:
        print("测试失败，没有获得有效结果")

if __name__ == "__main__":
    main()
