#!/usr/bin/env python3
"""
测试改进的MOSFET拟合算法
"""

import numpy as np
import matplotlib.pyplot as plt
from main import (
    actual_test_data, 
    estimate_initial_params, 
    fit_spice_params, 
    run_spice, 
    extract_data, 
    compare_simulation_with_actual,
    update_spice_params
)

def test_parameter_estimation():
    """测试参数估算功能"""
    print("=== 测试参数估算功能 ===")
    
    estimates = estimate_initial_params(actual_test_data)
    
    print("估算结果:")
    for key, value in estimates.items():
        print(f"  {key}: {value:.6f}")
    
    return estimates

def test_improved_fitting():
    """测试改进的拟合算法"""
    print("\n=== 测试改进的拟合算法 ===")
    
    # 获取参数估算
    param_estimates = estimate_initial_params(actual_test_data)
    
    # 使用改进的初始参数
    initial_params = {
        'param_MINT_Vto': param_estimates['estimated_vth'],
        'param_MINT_Kp': param_estimates['estimated_kp'],
        'param_MINT_U0': param_estimates['estimated_u0'],
        'param_MINT_Eta': param_estimates['estimated_eta'],
        'param_MINT_L': 1e-6,
        'param_MINT_W': 1e-4,
    }
    
    # 定义合理的参数边界
    param_bounds = {
        'param_MINT_Vto': (1.5, 3.5),
        'param_MINT_Kp': (20.0, 300.0),
        'param_MINT_U0': (200.0, 800.0),
        'param_MINT_Eta': (400.0, 1200.0),
        'param_MINT_L': (0.5e-6, 5e-6),
        'param_MINT_W': (10e-6, 1e-3),
    }
    
    print("开始拟合...")
    print(f"初始参数: {initial_params}")
    
    # 执行拟合（限制迭代次数以便快速测试）
    fitted_params, fit_info = fit_spice_params(
        initial_params=initial_params,
        param_bounds=param_bounds,
        max_iterations=50,  # 减少迭代次数用于测试
        tolerance=1e-6
    )
    
    print(f"拟合完成!")
    print(f"成功: {fit_info['success']}")
    print(f"RMS误差: {fit_info.get('rms_error', 'N/A')}")
    print(f"迭代次数: {fit_info.get('iterations', 'N/A')}")
    
    return fitted_params, fit_info

def compare_before_after():
    """比较改进前后的效果"""
    print("\n=== 比较改进前后的效果 ===")
    
    # 使用原始的简单参数进行对比
    simple_params = {
        'param_MINT_Vto': 2.8,
        'param_MINT_Kp': 100.0,
        'param_MINT_U0': 600.0,
        'param_MINT_Eta': 1000.0,
        'param_MINT_L': 1e-6,
        'param_MINT_W': 1e-4,
    }
    
    print("使用简单参数运行仿真...")
    update_spice_params("./ngspice_circuit/lib/POWERMOS.lib", **simple_params)
    run_spice()
    simple_data = extract_data("./ngspice_circuit/run_log.log")
    
    # 计算简单参数的误差
    if simple_data:
        sim_keys = list(simple_data.keys())
        if len(sim_keys) >= 2:
            sim_current = np.array(simple_data[sim_keys[1]])
            actual_current = actual_test_data['vids#branch']
            min_len = min(len(sim_current), len(actual_current))
            
            # 计算相对误差
            relative_errors = []
            for i in range(min_len):
                if actual_current[i] > 1e-12:
                    rel_err = abs((sim_current[i] - actual_current[i]) / actual_current[i])
                    relative_errors.append(rel_err)
            
            if relative_errors:
                simple_rms = np.sqrt(np.mean(np.array(relative_errors)**2))
                print(f"简单参数的RMS相对误差: {simple_rms:.6f}")
    
    return simple_data

def create_comparison_plot(simple_data, fitted_data=None):
    """创建对比图"""
    print("\n=== 创建对比图 ===")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 实际数据
    actual_voltage = actual_test_data['v-sweep']
    actual_current = actual_test_data['vids#branch']
    
    # 绘制实际数据
    ax1.semilogy(actual_voltage, actual_current, 'ro-', label='实际数据', markersize=4)
    ax2.semilogy(actual_voltage, actual_current, 'ro-', label='实际数据', markersize=4)
    
    # 绘制简单参数的仿真结果
    if simple_data:
        sim_keys = list(simple_data.keys())
        if len(sim_keys) >= 2:
            sim_voltage = np.array(simple_data[sim_keys[0]])
            sim_current = np.array(simple_data[sim_keys[1]])
            ax1.semilogy(sim_voltage, sim_current, 'b-', label='简单参数仿真', linewidth=2)
    
    # 绘制改进参数的仿真结果（如果有）
    if fitted_data:
        sim_keys = list(fitted_data.keys())
        if len(sim_keys) >= 2:
            sim_voltage = np.array(fitted_data[sim_keys[0]])
            sim_current = np.array(fitted_data[sim_keys[1]])
            ax2.semilogy(sim_voltage, sim_current, 'g-', label='改进参数仿真', linewidth=2)
    
    # 设置图表
    ax1.set_xlabel('栅极电压 Vgs (V)')
    ax1.set_ylabel('漏极电流 Id (A)')
    ax1.set_title('简单参数 vs 实际数据')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    ax2.set_xlabel('栅极电压 Vgs (V)')
    ax2.set_ylabel('漏极电流 Id (A)')
    ax2.set_title('改进参数 vs 实际数据')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    plt.tight_layout()
    plt.savefig('fitting_improvement_comparison.png', dpi=300, bbox_inches='tight')
    print("对比图已保存为 'fitting_improvement_comparison.png'")

def main():
    """主测试函数"""
    print("开始测试改进的MOSFET拟合算法...")
    
    # 测试参数估算
    estimates = test_parameter_estimation()
    
    # 测试改进的拟合
    fitted_params, fit_info = test_improved_fitting()
    
    # 比较改进前后
    simple_data = compare_before_after()
    
    # 如果拟合成功，运行最终仿真
    fitted_data = None
    if fit_info['success']:
        print("\n使用拟合参数运行最终仿真...")
        update_spice_params("./ngspice_circuit/lib/POWERMOS.lib", **fitted_params)
        run_spice()
        fitted_data = extract_data("./ngspice_circuit/run_log.log")
    
    # 创建对比图
    create_comparison_plot(simple_data, fitted_data)
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
