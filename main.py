import os
import re
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import least_squares

# 配置matplotlib字体设置，避免中文字体警告
def configure_matplotlib_fonts():
    """配置matplotlib字体，优先使用系统可用的字体"""
    import matplotlib.font_manager as fm

    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]

    # 定义字体优先级列表（包含中文和英文字体）
    font_candidates = [
        'DejaVu Sans',      # 默认英文字体
        'Arial',            # 常见英文字体
        'Liberation Sans',  # Linux常见字体
        'SimHei',          # Windows中文字体
        'WenQuanYi Micro Hei',  # Linux中文字体
        'Arial Unicode MS', # Mac中文字体
        'Noto Sans CJK SC', # Google中文字体
    ]

    # 选择第一个可用的字体
    selected_font = 'DejaVu Sans'  # 默认字体
    for font in font_candidates:
        if font in available_fonts:
            selected_font = font
            break

    # 设置matplotlib参数
    plt.rcParams['font.sans-serif'] = [selected_font]
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 10

    return selected_font

# 配置字体
selected_font = configure_matplotlib_fonts()
print(f"Using font: {selected_font}")

# 实际测试数据 (Id-Vg曲线，Vds=5V)
actual_test_data = {
    'v-sweep': np.array([0.0, 0.2, 0.4, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 1.8, 2.0, 2.2, 2.4, 2.6, 2.8, 3.0, 3.2, 3.4, 3.6, 3.8, 4.0, 4.2, 4.4, 4.6, 4.8, 5.0]),
    'vids#branch': np.array([5.2e-11, 5.2e-11, 5.2e-11, 5.2e-11, 5.2e-11, 5.3e-11, 5.5e-11, 6.8e-11, 2.1e-10, 8.9e-10, 3.2e-08, 1.8e-06, 9.5e-05, 4.2e-03, 1.8e-01, 7.8, 28.5, 62.1, 108.3, 167.2, 238.5, 321.8, 416.9, 523.4, 640.8, 769.2]) * 1e2  # 转换为安培
}

def run_spice():
    os.system("ngspice ./ngspice_circuit/demo.cir -b -o ./ngspice_circuit/run_log.log")


def plot_data(data_dict, x_key, y_keys, title="Data Plot", output_filename=None):
    """
    使用matplotlib根据提取的数据字典绘制曲线。

    参数:
    - data_dict (dict): 由 extract_data_modified 函数返回的数据字典。
    - x_key (str): 作为 X 轴的列名 (例如 'v-sweep')。
    - y_keys (list of str): 一个或多个作为 Y 轴的列名列表 (例如 ['vds_fixed#branc'])。
    - title (str, optional): 图表的标题。
    - output_filename (str, optional): 如果提供，图表将保存到此文件，而不是显示出来。
                                        例如 'my_plot.png'。
    """
    # 1. 输入验证
    if not data_dict:
        print("数据字典为空，无法绘图。")
        return

    if x_key not in data_dict:
        print(f"错误：X轴的键 '{x_key}' 不在数据中。")
        return

    # 为了方便，即使用户只提供一个y_key字符串，也将其转换为列表
    if isinstance(y_keys, str):
        y_keys = [y_keys]

    # 2. 创建图表和坐标轴
    fig, ax = plt.subplots(figsize=(10, 6)) # figsize可以调整图表大小

    # 3. 提取 X 轴数据
    x_values = data_dict[x_key]

    # 4. 循环绘制所有 Y 轴曲线
    for y_key in y_keys:
        if y_key in data_dict:
            y_values = data_dict[y_key]
            # 确保X和Y数据点数量一致
            if len(x_values) == len(y_values):
                ax.plot(x_values, y_values, marker='.', linestyle='-', label=y_key)
            else:
                print(f"警告：X轴 '{x_key}' 和 Y轴 '{y_key}' 的数据点数量不匹配，已跳过。")
        else:
            print(f"警告：Y轴的键 '{y_key}' 不在数据中，已跳过。")

    # 5. 美化图表
    ax.set_title(title, fontsize=16)
    ax.set_xlabel(x_key, fontsize=12)
    ax.set_ylabel("Values", fontsize=12)
    ax.grid(True, linestyle='--', alpha=0.6) # 添加网格线
    ax.legend() # 显示图例（基于plot中的label）

    # 6. 显示或保存图表
    if output_filename:
        try:
            plt.savefig(output_filename, dpi=300, bbox_inches='tight')
            print(f"图表已成功保存到 '{output_filename}'")
        except Exception as e:
            print(f"保存文件时出错: {e}")
    else:
        plt.show()


def extract_data(filename):
    with open(filename, "r") as f:
        lines = f.readlines()

    dicts = {}
    
    # 1. 找到所有分隔符行的索引
    separator = "--------------------------------------------------------------------------------"
    separator_indices = [i for i, line in enumerate(lines) if separator in line]

    # 2. 确保找到了至少两个分隔符
    if len(separator_indices) < 2:
        print("错误：未能找到定义数据块的两个分隔符。")
        return dicts  # 返回空字典

    # 3. 确定表头和数据区域的位置
    header_pos = separator_indices[0]
    data_start_pos = separator_indices[1]

    # 4. 提取并初始化表头 (keys)
    # 表头在第一个分隔符的下一行
    key_str = lines[header_pos + 1]
    keys = key_str.strip().split()
    
    # 从第二个 key 开始（跳过 'Index'）
    for key in keys[1:]:
        dicts[key] = []

    # 5. 遍历并提取数据行
    # 数据从第二个分隔符的下一行开始
    for line in lines[data_start_pos + 1:]:
        # 如果是空行或格式不对，就停止
        if not line.strip():
            break

        try:
            values = line.strip().split()
            # 确保行中有足够的数据列
            if len(values) < len(keys):
                continue # 如果列数不够，跳过此行

            # 遍历表头（从第二个开始），填充数据
            # 这里修正了原始代码的索引错误
            for i, key in enumerate(keys[1:]):
                # `keys[1]` 对应 `values[1]`, `keys[2]` 对应 `values[2]`, ...
                # `enumerate(keys[1:])` 中 i 从 0 开始，所以 `values` 的索引是 `i + 1`
                dicts[key].append(float(values[i + 1]))
        
        except (ValueError, IndexError):
            # 如果某一行无法转换为数字 (ValueError)，
            # 或者索引超出范围 (IndexError)，
            # 说明已经读到文件末尾的非数据部分，直接停止循环。
            break
            
    return dicts


def update_spice_params(lib_file_path, **params):
    """
    更新SPICE库文件中的参数值
    
    参数:
    - lib_file_path (str): SPICE库文件路径
    - **params: 要更新的参数，格式为 param_name=value
    
    示例:
    update_spice_params("./ngspice_circuit/lib/POWERMOS.lib", 
                       param_MINT_Vto=2.5, 
                       param_DBD_Bv=45.0)
    """
    try:
        # 读取文件内容
        with open(lib_file_path, 'r') as f:
            content = f.read()
        
        # 更新每个参数
        for param_name, param_value in params.items():
            # 构建正则表达式模式，匹配 .PARAM param_name=旧值
            pattern = rf'(\.PARAM\s+{re.escape(param_name)}\s*=\s*)([^\s\n]+)'
            
            # 替换参数值
            replacement = rf'\g<1>{param_value}'
            content = re.sub(pattern, replacement, content)
        
        # 写回文件
        with open(lib_file_path, 'w') as f:
            f.write(content)
            
        print(f"成功更新了 {len(params)} 个参数")
        for param_name, param_value in params.items():
            print(f"  {param_name} = {param_value}")
            
    except FileNotFoundError:
        print(f"错误：找不到文件 {lib_file_path}")
    except Exception as e:
        print(f"更新参数时出错: {e}")


def estimate_initial_params(actual_data):
    """
    基于实际数据特征估算更好的初始参数
    """
    voltage = actual_data['v-sweep']
    current = actual_data['vids#branch']

    # 找到阈值电压（使用更准确的方法）
    if len(current) > 5:
        # 方法1：寻找电流开始显著上升的点
        max_current = np.max(current)

        # 寻找电流超过最大值0.1%的第一个点作为导通开始
        threshold_current = max_current * 0.001
        threshold_idx = np.where(current > threshold_current)[0]

        if len(threshold_idx) > 0:
            # 在导通点附近寻找最大斜率点作为阈值电压
            start_idx = max(0, threshold_idx[0] - 2)
            end_idx = min(len(current) - 1, threshold_idx[0] + 10)

            if end_idx > start_idx + 3:
                # 计算斜率
                slopes = []
                for i in range(start_idx, end_idx - 1):
                    if voltage[i+1] != voltage[i]:
                        slope = (current[i+1] - current[i]) / (voltage[i+1] - voltage[i])
                        slopes.append((i, slope))

                if slopes:
                    # 找到最大斜率点
                    max_slope_idx = max(slopes, key=lambda x: x[1])[0]
                    estimated_vth = voltage[max_slope_idx]
                else:
                    estimated_vth = voltage[threshold_idx[0]]
            else:
                estimated_vth = voltage[threshold_idx[0]]
        else:
            # 备用方法：寻找电流超过1nA的点
            nanoamp_idx = np.where(current > 1e-9)[0]
            if len(nanoamp_idx) > 0:
                estimated_vth = voltage[nanoamp_idx[0]]
            else:
                estimated_vth = 2.4

        # 验证估算值的合理性
        if estimated_vth < 1.5 or estimated_vth > 4.0:
            estimated_vth = 2.4
    else:
        estimated_vth = 2.4

    # 估算跨导参数（基于亚阈值摆幅和线性区特性）
    if len(voltage) > 10:
        # 寻找亚阈值区域（电流在1nA到1μA之间）
        subthreshold_mask = (current > 1e-9) & (current < 1e-6)
        if np.any(subthreshold_mask):
            sub_voltage = voltage[subthreshold_mask]
            sub_current = current[subthreshold_mask]

            if len(sub_current) > 3:
                # 在亚阈值区域拟合指数关系
                log_current = np.log(sub_current)
                slope, _ = np.polyfit(sub_voltage, log_current, 1)
                # 从亚阈值摆幅估算跨导参数
                subthreshold_swing = 1.0 / slope  # V/decade
                estimated_kp = max(20.0, min(200.0, 100.0 / subthreshold_swing))
            else:
                estimated_kp = 80.0
        else:
            # 备用方法：使用线性区域的斜率
            linear_start = int(len(voltage) * 0.6)
            linear_end = int(len(voltage) * 0.9)
            if linear_end > linear_start + 2:
                dI_dV = (current[linear_end] - current[linear_start]) / (voltage[linear_end] - voltage[linear_start])
                estimated_kp = max(20.0, min(200.0, dI_dV * 0.1))
            else:
                estimated_kp = 80.0
    else:
        estimated_kp = 80.0

    # 估算其他关键参数
    max_current = np.max(current)

    # 估算载流子迁移率（基于最大电流）
    estimated_u0 = max(200.0, min(800.0, max_current * 1e6))

    # 估算亚阈值摆幅因子
    estimated_eta = max(400.0, min(1200.0, 1000.0 - estimated_vth * 100))

    return {
        'estimated_vth': estimated_vth,
        'estimated_kp': estimated_kp,
        'estimated_u0': estimated_u0,
        'estimated_eta': estimated_eta
    }


def fit_spice_params(initial_params, param_bounds=None, lib_file_path="./ngspice_circuit/lib/POWERMOS.lib",
                     actual_data=None, max_iterations=50, tolerance=1e-6):
    """
    使用最小二乘法拟合SPICE模型参数到实际测试数据

    参数:
    - initial_params (dict): 初始参数值，格式为 {'param_name': value}
    - param_bounds (dict, optional): 参数边界，格式为 {'param_name': (min_val, max_val)}
    - lib_file_path (str): SPICE库文件路径
    - actual_data (dict, optional): 实际测试数据，如果为None则使用全局的actual_test_data
    - max_iterations (int): 最大迭代次数
    - tolerance (float): 收敛容差

    返回:
    - fitted_params (dict): 拟合后的参数值
    - result_info (dict): 拟合结果信息（残差、迭代次数等）
    """

    if actual_data is None:
        actual_data = actual_test_data

    # 提取参数名称和初始值
    param_names = list(initial_params.keys())
    initial_values = [initial_params[name] for name in param_names]

    # 设置参数边界
    if param_bounds is not None:
        bounds = ([param_bounds[name][0] if name in param_bounds else -np.inf for name in param_names],
                 [param_bounds[name][1] if name in param_bounds else np.inf for name in param_names])
    else:
        bounds = (-np.inf, np.inf)

    def objective_function(params):
        """
        目标函数：计算仿真结果与实际数据的残差
        """
        try:
            # 构建参数字典
            param_dict = {param_names[i]: params[i] for i in range(len(param_names))}

            # 更新SPICE参数
            update_spice_params(lib_file_path, **param_dict)

            # 运行仿真
            run_spice()

            # 提取仿真数据
            sim_data = extract_data("./ngspice_circuit/run_log.log")

            if not sim_data:
                print("Warning: Failed to extract simulation data")
                return np.full(len(actual_data['vids#branch']), 1e6)  # 返回大的残差值

            # 获取仿真的电流数据（假设键名为第二个键）
            sim_keys = list(sim_data.keys())
            if len(sim_keys) < 2:
                print("Warning: Incorrect simulation data format")
                return np.full(len(actual_data['vids#branch']), 1e6)

            sim_current = np.array(sim_data[sim_keys[1]])  # 通常是电流数据
            actual_current = actual_data['vids#branch']

            # 确保数据长度一致并验证数据有效性
            min_len = min(len(sim_current), len(actual_current))
            sim_current = sim_current[:min_len]
            actual_current = actual_current[:min_len]

            # 验证数据有效性
            if min_len == 0:
                print("Warning: No data points to compare")
                return np.array([1e6])

            # 检查数据中是否有无效值
            if np.any(np.isnan(sim_current)) or np.any(np.isnan(actual_current)):
                print("Warning: NaN values detected in current data")
                return np.full(min_len, 1e6)

            if np.any(np.isinf(sim_current)) or np.any(np.isinf(actual_current)):
                print("Warning: Infinite values detected in current data")
                return np.full(min_len, 1e6)

            
            # 使用混合误差方法，重点关注阈值电压区域的匹配
            residuals = np.zeros(min_len)

            # 获取电压值
            voltage = actual_data['v-sweep'][:min_len]

            # 找到实际数据中电流开始显著上升的区域
            max_current = np.max(actual_current)
            threshold_current = max_current * 0.01  # 1%的最大电流作为阈值

            for i in range(min_len):
                actual_val = abs(actual_current[i])
                sim_val = abs(sim_current[i])
                vgs = voltage[i]

                # 添加小的正值避免除零
                actual_safe = max(actual_val, 1e-15)
                sim_safe = max(sim_val, 1e-15)

                # 根据电压和电流特征确定误差计算方法
                if actual_val < threshold_current:  # 亚阈值区域
                    # 使用对数误差
                    log_actual = np.log10(actual_safe)
                    log_sim = np.log10(sim_safe)
                    log_error = log_sim - log_actual

                    # 在预期阈值电压附近增加权重
                    if 2.2 <= vgs <= 2.8:
                        weight = 10.0  # 阈值区域超高权重
                    elif 2.0 <= vgs <= 3.0:
                        weight = 5.0   # 阈值附近高权重
                    else:
                        weight = 1.0   # 其他区域正常权重

                    residuals[i] = log_error * weight

                else:  # 导通区域
                    # 使用相对误差，但也要考虑对数误差
                    rel_error = (sim_val - actual_val) / actual_safe
                    log_actual = np.log10(actual_safe)
                    log_sim = np.log10(sim_safe)
                    log_error = log_sim - log_actual

                    # 混合误差：相对误差 + 对数误差
                    mixed_error = 0.7 * rel_error + 0.3 * log_error

                    # 在导通初期给予更高权重
                    if actual_val < threshold_current * 10:
                        weight = 8.0
                    elif actual_val < threshold_current * 100:
                        weight = 4.0
                    else:
                        weight = 2.0

                    residuals[i] = mixed_error * weight

            # 数值稳定性处理
            residuals = np.nan_to_num(residuals, nan=5.0, posinf=5.0, neginf=-5.0)
            residuals = np.clip(residuals, -15, 15)

            return residuals

        except Exception as e:
            print(f"Error in objective function calculation: {e}")
            return np.full(len(actual_data['vids#branch']), 1e6)  # 返回大的残差值

    print("Starting parameter fitting...")
    print(f"Initial parameters: {initial_params}")

    # 执行最小二乘优化，使用稳定的参数设置
    try:
        result = least_squares(objective_function,
                            initial_values,
                            bounds=bounds,
                            max_nfev=max_iterations * 5,  # 增加最大函数评估次数
                            ftol=1e-12,  # 更严格的函数容差
                            xtol=1e-12,  # 更严格的参数容差
                            gtol=1e-12,  # 更严格的梯度容差
                            verbose=2,
                            method='trf',  # Trust Region Reflective算法
                            loss='linear',  # 使用线性损失函数，更直接
                            f_scale=1.0,   # 设置特征尺度
                            diff_step=1e-8)  # 更小的差分步长

        # 构建拟合后的参数字典
        fitted_params = {param_names[i]: result.x[i] for i in range(len(param_names))}

        # 计算最终残差
        final_residuals = objective_function(result.x)
        rms_error = np.sqrt(np.mean(final_residuals**2))

        result_info = {
            'success': result.success,
            'message': result.message,
            'iterations': result.nfev,
            'rms_error': rms_error,
            'final_cost': result.cost,
            'residuals': final_residuals
        }

        print("Parameter fitting completed!")
        print(f"Fitted parameters: {fitted_params}")
        print(f"RMS error: {rms_error:.6f}")
        print(f"Iterations: {result.nfev}")
        print(f"Convergence status: {result.success}")

        return fitted_params, result_info

    except Exception as e:
        print(f"Error during parameter fitting: {e}")
        return initial_params, {'success': False, 'message': str(e)}


def compare_simulation_with_actual(sim_data, actual_data=None, output_filename=None):
    """
    比较仿真数据与实际测试数据，并绘制对比图

    参数:
    - sim_data (dict): 仿真数据字典
    - actual_data (dict, optional): 实际测试数据，如果为None则使用全局的actual_test_data
    - output_filename (str, optional): 输出图片文件名
    """
    if actual_data is None:
        actual_data = actual_test_data

    if not sim_data:
        print("Error: Simulation data is empty")
        return

    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # 提取仿真数据
    sim_keys = list(sim_data.keys())
    if len(sim_keys) >= 2:
        sim_voltage = np.array(sim_data[sim_keys[0]])
        sim_current = np.array(sim_data[sim_keys[1]])

        # 提取实际数据
        actual_voltage = actual_data['v-sweep']
        actual_current = actual_data['vids#branch']

        # 线性尺度对比图
        ax1.semilogy(sim_voltage, sim_current, 'b-', label='Simulation', linewidth=2)
        # ax1.plot(sim_voltage, sim_current, 'b-', label='Simulation', linewidth=2)
        ax1.plot(actual_voltage, actual_current, 'ro-', label='Measured Data', markersize=4)
        ax1.set_xlabel('Gate Voltage Vgs (V)')
        ax1.set_ylabel('Drain Current Id (A)')
        ax1.set_title('Id-Vg Characteristic Comparison (Linear Scale)')
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # 计算并显示误差
        # 插值仿真数据到实际数据的电压点
        if len(sim_voltage) > 1 and len(actual_voltage) > 1:
            sim_current_interp = np.interp(actual_voltage, sim_voltage, sim_current)

            # 计算相对误差
            relative_error = np.abs((sim_current_interp - actual_current) / (actual_current + 1e-15)) * 100

            ax2.plot(actual_voltage, relative_error, 'g-o', markersize=4)
            ax2.set_xlabel('Gate Voltage Vgs (V)')
            ax2.set_ylabel('Relative Error (%)')
            ax2.set_title('Fitting Relative Error')
            ax2.grid(True, alpha=0.3)

            # 计算平均误差
            mean_error = np.mean(relative_error)
            ax2.text(0.05, 0.95, f'Mean Relative Error: {mean_error:.2f}%',
                    transform=ax2.transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()

    if output_filename:
        try:
            plt.savefig(output_filename, dpi=300, bbox_inches='tight')
            print(f"Comparison plot saved to '{output_filename}'")
        except Exception as e:
            print(f"Error saving comparison plot: {e}")
    else:
        plt.show()


def main():
    # 示例：使用参数拟合功能

    # 首先基于实际数据估算更好的初始参数
    print("Estimating initial parameters based on actual data...")
    param_estimates = estimate_initial_params(actual_test_data)
    print(f"Estimated threshold voltage: {param_estimates['estimated_vth']:.3f}V")
    print(f"Estimated transconductance parameter: {param_estimates['estimated_kp']:.1f}")
    print(f"Estimated carrier mobility: {param_estimates['estimated_u0']:.1f}")
    print(f"Estimated subthreshold factor: {param_estimates['estimated_eta']:.1f}")

    # 定义要拟合的初始参数（基于实际数据特征调整）
    initial_params = {
        'param_MINT_Vto': 2.4,      # 阈值电压（更接近实际数据）
        'param_MINT_Kp': 50e-6,     # 跨导参数（使用微安级别）
        'param_MINT_U0': 600.0,     # 载流子迁移率
        'param_MINT_Nfs': 1e11,     # 快态密度
        'param_MINT_Eta': 1000.0,   # 亚阈值摆幅因子
        'param_MINT_L': 2e6,       # 门长度
        'param_MINT_W': 1e4,       # 门宽度
        'param_MINT_Gamma': 0.5,    # 体效应参数
        'param_MINT_Phi': 0.7,      # 费米势
        'param_MINT_Is': 1e-14,     # 反向饱和电流
        'param_MINT_Js': 1e-8,      # 结电流密度
        'param_MINT_Pb': 0.8,       # 结内建电势
        'param_MINT_Tox': 10e-9,    # 栅氧化层厚度
        'param_MINT_Xj': 0.2e-6,    # 结深度
        'param_MINT_Vmax': 1e5      # 载流子饱和速度
    }

    # 定义参数边界（扩大搜索范围以找到真正的最优解）
    param_bounds = {
        'param_MINT_Vto': (1.5, 4.0),      # 阈值电压范围（扩大范围）
        'param_MINT_Kp': (1e-6, 100.0),    # 跨导参数范围（扩大范围）
        'param_MINT_U0': (200.0, 1000.0),  # 载流子迁移率范围
        'param_MINT_Nfs': (1e10, 1e12),    # 快态密度范围
        'param_MINT_Eta': (100.0, 2000.0), # 亚阈值摆幅因子范围
        'param_MINT_L': (0.5e-6, 10e10),   # 门长度范围
        'param_MINT_W': (1e-5, 5e10),      # 门宽度范围
        'param_MINT_Gamma': (0.1, 1.5),    # 体效应参数范围
        'param_MINT_Phi': (0.3, 1.2),      # 费米势范围
        'param_MINT_Is': (1e-18, 1e-10),   # 反向饱和电流范围
        'param_MINT_Js': (1e-12, 1e-6),    # 结电流密度范围
        'param_MINT_Pb': (0.5, 1.2),       # 结内建电势范围
        'param_MINT_Tox': (2e-9, 50e-9),   # 栅氧化层厚度范围
        'param_MINT_Xj': (0.05e-6, 1e-6),  # 结深度范围
        'param_MINT_Vmax': (1e4, 5e5)      # 载流子饱和速度范围
    }

    # 执行参数拟合，使用更合理的容差设置
    fitted_params, fit_info = fit_spice_params(
        initial_params=initial_params,
        param_bounds=param_bounds,
        max_iterations=1000,
        tolerance=1e-8  # 更合理的容差，避免过度优化
    )

    # 使用拟合后的参数运行最终仿真
    print("\nRunning final simulation with fitted parameters...")
    update_spice_params("./ngspice_circuit/lib/POWERMOS.lib", **fitted_params)
    run_spice()
    dicts = extract_data("./ngspice_circuit/run_log.log")

    # 绘制拟合结果对比图
    compare_simulation_with_actual(dicts, output_filename="mosfet_fitting_comparison.png")

    # 可选：也可以运行原始的示例代码
    # update_spice_params("./ngspice_circuit/lib/POWERMOS.lib",
    #                    param_MINT_Vto=2.8,
    #                    param_DBD_Bv=50.0,
    #                    param_DBGS_Bv=40.0)
    #
    # run_spice()
    # dicts = extract_data("./ngspice_circuit/run_log.log")
    # keys = list(dicts.keys())
    # plot_data(dicts, keys[0], keys[1], title="MOSFET Transfer Characteristic (Vds = 5V)",
    #           output_filename="mosfet_transfer_char.png")


if __name__ == "__main__":
    main()


