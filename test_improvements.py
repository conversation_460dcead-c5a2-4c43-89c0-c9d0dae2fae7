#!/usr/bin/env python3
"""
Test script to verify the numerical improvements in the residual calculation
"""

import numpy as np
import matplotlib.pyplot as plt

# Test data similar to the actual MOSFET data
test_actual_data = {
    'v-sweep': np.array([0.0, 0.2, 0.4, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 1.8, 2.0, 2.2, 2.4, 2.6, 2.8, 3.0]),
    'vids#branch': np.array([5.2e-11, 5.2e-11, 5.2e-11, 5.2e-11, 5.2e-11, 5.3e-11, 5.5e-11, 6.8e-11, 2.1e-10, 8.9e-10, 3.2e-08, 1.8e-06, 9.5e-05, 4.2e-03, 1.8e-01, 7.8]) * 1e2
}

# Simulated data with some differences
test_sim_data = test_actual_data['vids#branch'] * (1 + 0.1 * np.random.randn(len(test_actual_data['vids#branch'])))

def improved_residual_calculation(sim_current, actual_current):
    """
    Improved residual calculation with numerical stability
    """
    min_len = min(len(sim_current), len(actual_current))
    sim_current = sim_current[:min_len]
    actual_current = actual_current[:min_len]
    
    # Validate data
    if min_len == 0:
        return np.array([1e6])
    
    if np.any(np.isnan(sim_current)) or np.any(np.isnan(actual_current)):
        return np.full(min_len, 1e6)
    
    if np.any(np.isinf(sim_current)) or np.any(np.isinf(actual_current)):
        return np.full(min_len, 1e6)
    
    # Calculate improved residuals with multi-scale weighted approach
    residuals = np.zeros(min_len)
    
    for i in range(min_len):
        actual_val = abs(actual_current[i])
        sim_val = abs(sim_current[i])
        
        # Add small positive value to avoid division by zero and log calculation issues
        actual_safe = max(actual_val, 1e-15)
        sim_safe = max(sim_val, 1e-15)
        
        if actual_val < 1e-9:  # Sub-threshold region (< 1nA)
            try:
                log_actual = np.log10(actual_safe)
                log_sim = np.log10(sim_safe)
                log_diff = log_sim - log_actual
                # Limit log difference range to avoid numerical instability
                residuals[i] = np.clip(log_diff, -10, 10) * 0.5  # Reduce weight
            except (OverflowError, ValueError):
                residuals[i] = 10.0  # Assign large residual value
                
        elif actual_val < 1e-3:  # Medium current region (1nA - 1mA)
            # Use stable relative error calculation
            relative_error = (sim_val - actual_val) / (actual_safe + 1e-12)
            # Limit relative error range
            residuals[i] = np.clip(relative_error, -100, 100) * 10.0
            
        else:  # High current region (> 1mA)
            # Use normalized relative error with increased weight
            relative_error = (sim_val - actual_val) / actual_safe
            # Limit relative error and increase weight
            residuals[i] = np.clip(relative_error, -10, 10) * 100.0
    
    # Check and handle any NaN or infinite values
    residuals = np.nan_to_num(residuals, nan=100.0, posinf=100.0, neginf=-100.0)
    
    # Apply additional numerical stability check
    if np.any(np.abs(residuals) > 1e6):
        print("Warning: Large residuals detected, applying clipping")
        residuals = np.clip(residuals, -1e3, 1e3)
    
    return residuals

def old_residual_calculation(sim_current, actual_current):
    """
    Original residual calculation method for comparison
    """
    min_len = min(len(sim_current), len(actual_current))
    sim_current = sim_current[:min_len]
    actual_current = actual_current[:min_len]
    
    # Original method using log scale
    sim_log = np.log10(np.maximum(sim_current, 1e-15))
    actual_log = np.log10(np.maximum(actual_current, 1e-15))
    residuals = sim_log - actual_log
    
    return residuals

def test_residual_methods():
    """
    Test and compare the old and new residual calculation methods
    """
    print("Testing residual calculation methods...")
    
    actual_current = test_actual_data['vids#branch']
    sim_current = test_sim_data
    
    print(f"Data range: {np.min(actual_current):.2e} to {np.max(actual_current):.2e} A")
    
    # Test old method
    try:
        old_residuals = old_residual_calculation(sim_current, actual_current)
        old_rms = np.sqrt(np.mean(old_residuals**2))
        old_max = np.max(np.abs(old_residuals))
        print(f"Old method - RMS: {old_rms:.6f}, Max residual: {old_max:.6f}")
        old_has_issues = np.any(np.isnan(old_residuals)) or np.any(np.isinf(old_residuals))
        print(f"Old method has numerical issues: {old_has_issues}")
    except Exception as e:
        print(f"Old method failed with error: {e}")
        old_residuals = None
    
    # Test new method
    try:
        new_residuals = improved_residual_calculation(sim_current, actual_current)
        new_rms = np.sqrt(np.mean(new_residuals**2))
        new_max = np.max(np.abs(new_residuals))
        print(f"New method - RMS: {new_rms:.6f}, Max residual: {new_max:.6f}")
        new_has_issues = np.any(np.isnan(new_residuals)) or np.any(np.isinf(new_residuals))
        print(f"New method has numerical issues: {new_has_issues}")
    except Exception as e:
        print(f"New method failed with error: {e}")
        new_residuals = None
    
    # Plot comparison if both methods work
    if old_residuals is not None and new_residuals is not None:
        plt.figure(figsize=(12, 5))
        
        plt.subplot(1, 2, 1)
        plt.plot(test_actual_data['v-sweep'], old_residuals, 'r-o', label='Old Method')
        plt.xlabel('Gate Voltage (V)')
        plt.ylabel('Residuals')
        plt.title('Old Residual Calculation')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        plt.subplot(1, 2, 2)
        plt.plot(test_actual_data['v-sweep'], new_residuals, 'b-o', label='New Method')
        plt.xlabel('Gate Voltage (V)')
        plt.ylabel('Residuals')
        plt.title('Improved Residual Calculation')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        plt.tight_layout()
        plt.savefig('residual_comparison.png', dpi=300, bbox_inches='tight')
        print("Residual comparison plot saved as 'residual_comparison.png'")
        plt.show()

def estimate_initial_params_test():
    """
    Test the initial parameter estimation function
    """
    from main import estimate_initial_params
    
    print("\nTesting initial parameter estimation...")
    estimates = estimate_initial_params(test_actual_data)
    print(f"Estimated threshold voltage: {estimates['estimated_vth']:.3f}V")
    print(f"Estimated transconductance parameter: {estimates['estimated_kp']:.1f}")

if __name__ == "__main__":
    test_residual_methods()
    estimate_initial_params_test()
    print("\nTest completed successfully!")
